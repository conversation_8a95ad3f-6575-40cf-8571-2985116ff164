---
inclusion: always
---

# Technology Stack & Development Standards

## Core Technology Stack

### Backend Framework
- **Python >=3.9** - Primary development language
- **Flask 2.3.3** - Email inbox management service (port 5000)
- **FastAPI 0.104.1** - FT-EQC processing service (port 8010)
- **SQLAlchemy 2.0.23** - Database ORM with type hints
- **Pydantic 2.5.0** - Data validation and serialization

### Data Processing
- **Pandas 2.1.3** - Data manipulation and analysis
- **OpenPyXL 3.1.2** - Excel file processing
- **XlsxWriter 3.1.9** - Excel report generation
- **NumPy 1.24.3** - Numerical computations

### Testing & Quality Tools
- **Pytest** - Testing framework
- **Playwright** - End-to-end testing
- **Black** - Code formatting (mandatory)
- **Flake8** - Code linting (mandatory)
- **MyPy** - Type checking (mandatory)
- **Loguru** - Structured logging

### AI/LLM Integration
- **Ollama** - Local LLM provider (default)
- **Grok API** - Alternative LLM provider
- **Sentence Transformers** - Text embeddings

## Mandatory Development Practices

### Code Quality Requirements
- **Virtual Environment**: MUST be activated before development
- **TDD Approach**: Write tests before implementation
- **Type Hints**: ALL functions must have complete type annotations
- **Code Formatting**: MUST pass Black + Flake8 validation
- **Test Coverage**: Minimum 90% for core business logic, 95% for domain logic

### Quality Gates
- All tests MUST pass
- MyPy type checking: 100% compliance
- No high-risk security issues (Bandit scan)
- Code formatting: Black + Flake8 compliant

## Architecture Compliance

### Hexagonal Architecture Rules
- Domain layer contains NO external dependencies
- Infrastructure adapts external systems to domain interfaces
- Application layer orchestrates use cases
- Dependencies MUST point inward toward domain
- Use constructor-based dependency injection throughout

### Service Architecture
- **Flask service (Port 5000)**: Email inbox management
- **FastAPI service (Port 8010)**: FT-EQC processing with `/ui` and `/docs`
- Maintain clear service boundaries and responsibilities

## Platform Considerations

### Windows Compatibility (CRITICAL)
- MUST handle UTF-8 encoding: `PYTHONIOENCODING=utf-8` and `PYTHONUTF8=1`
- Use `pathlib.Path` for cross-platform file paths
- Handle Windows path separators correctly

### Development Environment
- Virtual environment activation is MANDATORY
- Use `venv_win_3_11_12\Scripts\activate` for Windows UV environment
- SQLite for development/testing, PostgreSQL for production